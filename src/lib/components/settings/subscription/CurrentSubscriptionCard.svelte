<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { t } from '$lib/stores/i18n';
	import type { CurrentSubscription, NotificationItem } from '$lib/types/subscription';
	import { But<PERSON>, Badge } from 'flowbite-svelte';
	import { 
		ShieldCheckSolid, 
		CalendarMonthSolid, 
		ExclamationCircleSolid,
		CheckCircleSolid,
		InfoCircleSolid
	} from 'flowbite-svelte-icons';

	export let subscription: CurrentSubscription;
	export let notifications: NotificationItem[] = [];

	const dispatch = createEventDispatcher();

	// Status configuration
	const statusConfig = {
		active: { 
			color: 'green', 
			icon: CheckCircleSolid,
			label: 'Active',
			bgClass: 'bg-green-100 text-green-800 border-green-200'
		},
		expired: { 
			color: 'red', 
			icon: ExclamationCircleSolid,
			label: 'Expired',
			bgClass: 'bg-red-100 text-red-800 border-red-200'
		},
		pending: { 
			color: 'yellow', 
			icon: InfoCircleSolid,
			label: 'Pending',
			bgClass: 'bg-yellow-100 text-yellow-800 border-yellow-200'
		},
		cancelled: { 
			color: 'gray', 
			icon: ExclamationCircleSolid,
			label: 'Cancelled',
			bgClass: 'bg-gray-100 text-gray-800 border-gray-200'
		}
	};

	$: currentStatus = statusConfig[subscription.status as keyof typeof statusConfig] || statusConfig.active;
	$: isExpiringSoon = checkExpirationStatus(subscription.expiresAt);
	$: daysUntilExpiry = getDaysUntilExpiry(subscription.expiresAt);

	function checkExpirationStatus(expiresAt: string): boolean {
		const expiryDate = new Date(expiresAt);
		const today = new Date();
		const diffTime = expiryDate.getTime() - today.getTime();
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
		return diffDays <= 30 && diffDays > 0;
	}

	function getDaysUntilExpiry(expiresAt: string): number {
		const expiryDate = new Date(expiresAt);
		const today = new Date();
		const diffTime = expiryDate.getTime() - today.getTime();
		return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
	}

	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'long',
			day: 'numeric'
		});
	}

	function handleAction(action: string, data?: any) {
		dispatch('action', { action, data });
	}

	function getNotificationIcon(type: string) {
		switch (type) {
			case 'success': return CheckCircleSolid;
			case 'warning': return ExclamationCircleSolid;
			case 'info': 
			default: return InfoCircleSolid;
		}
	}

	function getNotificationClass(type: string): string {
		switch (type) {
			case 'success': return 'bg-green-50 border-green-200 text-green-800';
			case 'warning': return 'bg-yellow-50 border-yellow-200 text-yellow-800';
			case 'error': return 'bg-red-50 border-red-200 text-red-800';
			case 'info':
			default: return 'bg-blue-50 border-blue-200 text-blue-800';
		}
	}
</script>

<div class="space-y-6">
	<!-- Current Subscription Card -->
	<div class="bg-white rounded-lg shadow-md border p-6">
		<div class="flex items-center justify-between mb-6">
			<div class="flex items-center space-x-3">
				<!-- <div class="p-2 bg-blue-100 rounded-lg">
					<ShieldCheckSolid class="h-6 w-6 text-blue-600" />
				</div> -->
				<div>
					<h2 class="text-xl font-semibold text-gray-900">
						{t('subscription_current_package') || 'Current Subscription'}
					</h2>
					<!-- <p class="text-sm text-gray-600">
						{t('subscription_details') || 'Your active subscription details'}
					</p> -->
				</div>
			</div>
			
			<!-- Status Badge -->
			<div class="flex items-center space-x-1">
				<svelte:component 
					this={currentStatus.icon} 
					class="h-4 w-4 text-{currentStatus.color}-600" 
				/>
				<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border {currentStatus.bgClass}">
					{t(currentStatus.label.toLowerCase()) || currentStatus.label}
				</span>
			</div>
		</div>

		<!-- Subscription Info Grid -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
			<!-- Company -->
			<div class="text-center p-4 bg-gray-50 rounded-lg">
				<h3 class="text-sm font-medium text-gray-500 mb-1">
					{t('subscription_company')}
				</h3>
				<p class="text-lg font-semibold text-gray-900">{subscription.organizationName}</p>
			</div>

			<!-- Plan Tier -->
			<div class="text-center p-4 bg-gray-50 rounded-lg">
				<h3 class="text-sm font-medium text-gray-500 mb-1">
					{t('subscription_tier')}
				</h3>
				<p class="text-lg font-semibold text-gray-900">{subscription.tier}</p>
			</div>

			<!-- Serial Number -->
			<!-- <div class="text-center p-4 bg-gray-50 rounded-lg">
				<h3 class="text-sm font-medium text-gray-500 mb-1">
					{t('subscription_serial_number')}
				</h3>
				<p class="text-sm font-mono text-gray-900">{subscription.serialNumber}</p>
			</div> -->

			<!-- Expiry Date -->
			<div class="text-center p-4 bg-gray-50 rounded-lg">
				<h3 class="text-sm font-medium text-gray-500 mb-1">
					{t('subscription_expires_on')}
				</h3>
				<p class="text-lg font-semibold text-gray-900">{formatDate(subscription.expiresAt)}</p>
			</div>

			<!-- Days Remaining -->
			<div class="text-center p-4 bg-gray-50 rounded-lg">
				<h3 class="text-sm font-medium text-gray-500 mb-1">
					{t('subscription_days_remaining')}
				</h3>
				<p class="text-lg font-semibold {isExpiringSoon ? 'text-yellow-600' : 'text-gray-900'}">
					{daysUntilExpiry > 0 ? daysUntilExpiry : t('subscription_expired')}
				</p>
			</div>
		</div>

		<!-- Expiration Warning -->
		{#if isExpiringSoon}
			<div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg mt-6">
				<div class="flex items-center">
					<ExclamationCircleSolid class="h-5 w-5 text-yellow-600 mr-3" />
					<div>
						<h4 class="text-sm font-medium text-yellow-800">
							{t('subscription_expiring_soon')}
						</h4>
						<p class="text-sm text-yellow-700 mt-1">
							{t('subscription_expiry_warning')}
						</p>
					</div>
				</div>
			</div>
		{/if}

		<!-- Action Buttons -->
		<!-- <div class="flex flex-wrap gap-3">
			<Button 
				color="blue" 
				size="sm"
				on:click={() => handleAction('upgrade')}
			>
				{t('upgrade_plan') || 'Upgrade Plan'}
			</Button>
			
			<Button 
				color="alternative" 
				size="sm"
				on:click={() => handleAction('renew')}
			>
				{t('renew_subscription') || 'Renew Subscription'}
			</Button>
		</div> -->
	</div>

	<!-- Notifications -->
	<!-- {#if notifications && notifications.length > 0}
		<div class="bg-white rounded-lg shadow-md border p-6">
			<h3 class="text-lg font-semibold text-gray-900 mb-4">
				{t('recent_notifications') || 'Recent Notifications'}
			</h3>
			<div class="space-y-3">
				{#each notifications as notification}
					<div class="flex items-start space-x-3 p-3 rounded-lg border {getNotificationClass(notification.type)}">
						<svelte:component 
							this={getNotificationIcon(notification.type)} 
							class="h-5 w-5 mt-0.5 flex-shrink-0" 
						/>
						<div class="flex-1 min-w-0">
							<p class="text-sm font-medium">{notification.message}</p>
							<p class="text-xs opacity-75 mt-1">
								{new Date(notification.timestamp).toLocaleDateString()}
							</p>
						</div>
					</div>
				{/each}
			</div>
		</div>
	{/if} -->
</div>
